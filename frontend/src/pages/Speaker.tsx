import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";

const LANGUAGES = [
  { code: "en", name: "English" },
  { code: "zh", name: "Chinese" },
  { code: "es", name: "Spanish" },
  { code: "fr", name: "French" },
  { code: "de", name: "German" },
  { code: "ja", name: "Japanese" },
  { code: "ko", name: "Korean" },
  { code: "ar", name: "Arabic" },
];

const Speaker = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [sessionCode] = useState(() => Math.random().toString(36).substr(2, 6).toUpperCase());
  const [currentStep, setCurrentStep] = useState<"setup" | "speaking">("setup");
  const [inputLanguage, setInputLanguage] = useState("en");
  const [outputLanguage, setOutputLanguage] = useState("zh");
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined" && ("webkitSpeechRecognition" in window || "SpeechRecognition" in window)) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = inputLanguage;

      recognitionRef.current.onresult = (event) => {
        let finalTranscript = "";
        for (let i = event.resultIndex; i < event.results.length; i++) {
          if (event.results[i].isFinal) {
            finalTranscript += event.results[i][0].transcript;
          }
        }
        if (finalTranscript) {
          setTranscript(finalTranscript);
        }
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };

      recognitionRef.current.onerror = () => {
        setIsListening(false);
        toast({
          title: "Speech recognition error",
          description: "Please check your microphone permissions and try again.",
          variant: "destructive",
        });
      };
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [inputLanguage, toast]);

  const startSession = () => {
    setCurrentStep("speaking");
  };

  const toggleListening = async () => {
    if (!recognitionRef.current) {
      toast({
        title: "Speech recognition not supported",
        description: "Please use a supported browser like Chrome or Edge.",
        variant: "destructive",
      });
      return;
    }

    if (isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    } else {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true });
        recognitionRef.current.start();
        setIsListening(true);
      } catch (error) {
        toast({
          title: "Microphone access denied",
          description: "Please allow microphone access to use speech recognition.",
          variant: "destructive",
        });
      }
    }
  };

  const copySessionCode = () => {
    navigator.clipboard.writeText(sessionCode);
    toast({
      title: "Session code copied",
      description: "Share this code with your audience to let them join.",
    });
  };

  if (currentStep === "setup") {
    return (
      <div className="min-h-screen bg-gradient-surface p-4">
        <div className="container mx-auto max-w-2xl pt-8">
          <Button
            variant="ghost"
            onClick={() => navigate("/")}
            className="mb-6 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>

          <Card className="shadow-medium border-border/50">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl text-foreground">Create Speaking Session</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center p-6 bg-primary/5 rounded-lg border border-primary/20">
                <h3 className="text-lg font-semibold text-foreground mb-2">Session Code</h3>
                <div className="flex items-center justify-center gap-3">
                  <Badge variant="outline" className="text-2xl font-mono px-4 py-2 bg-background">
                    {sessionCode}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copySessionCode}
                    className="text-primary border-primary/30 hover:bg-primary/10"
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Share this code with your audience
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">Input Language</label>
                  <Select value={inputLanguage} onValueChange={setInputLanguage}>
                    <SelectTrigger className="bg-background border-border">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {LANGUAGES.map((lang) => (
                        <SelectItem key={lang.code} value={lang.code}>
                          {lang.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">Output Language</label>
                  <Select value={outputLanguage} onValueChange={setOutputLanguage}>
                    <SelectTrigger className="bg-background border-border">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {LANGUAGES.map((lang) => (
                        <SelectItem key={lang.code} value={lang.code}>
                          {lang.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button
                onClick={startSession}
                className="w-full bg-gradient-primary hover:opacity-90 text-white font-medium"
                size="lg"
              >
                Start Session
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-surface p-4">
      <div className="container mx-auto max-w-4xl pt-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold text-foreground">Speaking Session</h1>
            <p className="text-muted-foreground">Session Code: <span className="font-mono font-medium">{sessionCode}</span></p>
          </div>
          <Button
            variant="outline"
            onClick={() => setCurrentStep("setup")}
            className="text-muted-foreground"
          >
            Settings
          </Button>
        </div>

        <div className="grid gap-6">
          <Card className="shadow-medium border-border/50">
            <CardHeader>
              <CardTitle className="text-center">Voice Input</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <Button
                onClick={toggleListening}
                size="lg"
                className={`w-24 h-24 rounded-full ${
                  isListening
                    ? "bg-destructive hover:bg-destructive/90 text-destructive-foreground animate-pulse"
                    : "bg-gradient-primary hover:opacity-90 text-white"
                }`}
              >
                {isListening ? <MicOff className="w-8 h-8" /> : <Mic className="w-8 h-8" />}
              </Button>
              <p className="text-sm text-muted-foreground">
                {isListening ? "Listening... Click to stop" : "Click to start speaking"}
              </p>
            </CardContent>
          </Card>

          {transcript && (
            <Card className="shadow-medium border-border/50">
              <CardHeader>
                <CardTitle>Current Speech</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-foreground bg-muted/50 p-4 rounded-lg border">
                  {transcript}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default Speaker;