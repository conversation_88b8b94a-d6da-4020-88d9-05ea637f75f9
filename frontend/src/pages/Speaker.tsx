import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ArrowL<PERSON>t, Users } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useWebSocket } from "@/hooks/use-websocket";
import { useAudioRecorder } from "@/hooks/use-audio-recorder";

const LANGUAGES = [
  { code: "en", name: "English" },
  { code: "zh", name: "Chinese" },
  { code: "es", name: "Spanish" },
  { code: "fr", name: "French" },
  { code: "de", name: "German" },
  { code: "ja", name: "Japanese" },
  { code: "ko", name: "Korean" },
  { code: "ar", name: "Arabic" },
];

const Speaker = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [sessionCode, setSessionCode] = useState("");
  const [currentStep, setCurrentStep] = useState<"setup" | "speaking" | "rejoin">("setup");
  const [inputLanguage, setInputLanguage] = useState("en");
  const [outputLanguage, setOutputLanguage] = useState("zh");
  const [isRecording, setIsRecording] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [audienceCount, setAudienceCount] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<"disconnected" | "connecting" | "connected">("disconnected");
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [rejoinCode, setRejoinCode] = useState("");

  // WebSocket connection for speaker
  const wsUrl = currentStep === "speaking"
    ? `ws://localhost:8081/ws/speaker/${sessionCode}`
    : null;

  const { isConnected, sendBinaryData, sendMessage } = useWebSocket(wsUrl, {
    onOpen: () => {
      setConnectionStatus("connected");
      toast({
        title: "Connected to session",
        description: "You can now start speaking to your audience.",
      });
    },
    onMessage: (message) => {
      if (message.type === "PartialTranscript" && message.data?.segments) {
        const latestSegment = message.data.segments[message.data.segments.length - 1];
        if (latestSegment?.text) {
          setTranscript(latestSegment.text);
        }
      } else if (message.type === "audience_count") {
        setAudienceCount(message.data || 0);
      }
    },
    onError: () => {
      setConnectionStatus("disconnected");
      toast({
        title: "Connection error",
        description: "Failed to connect to the session. Please try again.",
        variant: "destructive",
      });
    },
    onClose: () => {
      setConnectionStatus("disconnected");
    },
  });

  // Audio recording
  const { startRecording, stopRecording, error: audioError } = useAudioRecorder({
    sampleRate: 16000,
    channels: 1,
    onDataAvailable: (audioData) => {
      if (isConnected) {
        sendBinaryData(audioData);
      }
    },
    onError: (error) => {
      toast({
        title: "Audio recording error",
        description: error,
        variant: "destructive",
      });
    },
  });

  useEffect(() => {
    if (currentStep === "speaking") {
      setConnectionStatus("connecting");
    }
  }, [currentStep]);

  const handleDisconnect = () => {
    if (isRecording) {
      stopRecording();
      setIsRecording(false);
    }
    setCurrentStep("setup");
    setConnectionStatus("disconnected");
    setTranscript("");
    setAudienceCount(0);

    toast({
      title: "Session ended",
      description: "You can rejoin using the same session code if needed.",
    });
  };

  const startSession = async () => {
    try {
      // Create session on backend with language preferences
      const response = await fetch('http://localhost:8081/session/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input_language: inputLanguage,
          output_language: outputLanguage,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSessionCode(data.code);
        setSessionInfo(data.session);
        setCurrentStep("speaking");

        toast({
          title: "Session created",
          description: `Session code: ${data.code}`,
        });
      } else {
        toast({
          title: "Failed to create session",
          description: "Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Connection error",
        description: "Could not connect to the server. Please check if the backend is running.",
        variant: "destructive",
      });
    }
  };

  const rejoinSession = async () => {
    if (!rejoinCode.trim()) {
      toast({
        title: "Session code required",
        description: "Please enter a session code to rejoin.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Rejoin existing session
      const response = await fetch(`http://localhost:8081/session/speaker/rejoin/${rejoinCode}`, {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        setSessionCode(rejoinCode);
        setSessionInfo(data.session);
        setInputLanguage(data.session.input_language || "en");
        setOutputLanguage(data.session.output_language || "zh");
        setCurrentStep("speaking");

        toast({
          title: "Rejoined session",
          description: `Reconnected to session ${rejoinCode}`,
        });
      } else {
        const errorData = await response.json();
        toast({
          title: "Failed to rejoin session",
          description: errorData.error || "Session not found or inactive.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Connection error",
        description: "Could not connect to the server. Please check if the backend is running.",
        variant: "destructive",
      });
    }
  };

  const toggleRecording = async () => {
    if (!isConnected) {
      toast({
        title: "Not connected",
        description: "Please wait for the connection to be established.",
        variant: "destructive",
      });
      return;
    }

    if (isRecording) {
      stopRecording();
      setIsRecording(false);
      // Send END signal to backend
      sendMessage("END");
    } else {
      try {
        await startRecording();
        setIsRecording(true);
      } catch (error) {
        toast({
          title: "Failed to start recording",
          description: "Please check your microphone permissions and try again.",
          variant: "destructive",
        });
      }
    }
  };

  const copySessionCode = () => {
    navigator.clipboard.writeText(sessionCode);
    toast({
      title: "Session code copied",
      description: "Share this code with your audience to let them join.",
    });
  };

  if (currentStep === "setup") {
    return (
      <div className="min-h-screen bg-gradient-surface p-4">
        <div className="container mx-auto max-w-2xl pt-8">
          <Button
            variant="ghost"
            onClick={() => navigate("/")}
            className="mb-6 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>

          <div className="space-y-6">
            <Card className="shadow-medium border-border/50">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-foreground">Create New Session</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-foreground">Input Language</label>
                    <Select value={inputLanguage} onValueChange={setInputLanguage}>
                      <SelectTrigger className="bg-background border-border">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {LANGUAGES.map((lang) => (
                          <SelectItem key={lang.code} value={lang.code}>
                            {lang.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-foreground">Output Language</label>
                    <Select value={outputLanguage} onValueChange={setOutputLanguage}>
                      <SelectTrigger className="bg-background border-border">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {LANGUAGES.map((lang) => (
                          <SelectItem key={lang.code} value={lang.code}>
                            {lang.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button
                  onClick={startSession}
                  className="w-full bg-gradient-primary hover:opacity-90 text-white font-medium"
                  size="lg"
                >
                  Create New Session
                </Button>
              </CardContent>
            </Card>

            <Card className="shadow-medium border-border/50">
              <CardHeader className="text-center">
                <CardTitle className="text-xl text-foreground">Rejoin Existing Session</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">Session Code</label>
                  <input
                    type="text"
                    placeholder="Enter session code"
                    value={rejoinCode}
                    onChange={(e) => setRejoinCode(e.target.value.toUpperCase())}
                    className="w-full px-3 py-2 text-center text-lg font-mono tracking-wider bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    maxLength={8}
                  />
                  <p className="text-xs text-muted-foreground text-center">
                    Enter the code from your previous session
                  </p>
                </div>

                <Button
                  onClick={rejoinSession}
                  variant="outline"
                  className="w-full border-primary/30 text-primary hover:bg-primary/10"
                  size="lg"
                >
                  Rejoin Session
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-surface p-4">
      <div className="container mx-auto max-w-4xl pt-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold text-foreground">Speaking Session</h1>
            <div className="flex items-center gap-4 mt-1">
              <p className="text-muted-foreground">
                Session Code: <span className="font-mono font-medium text-primary">{sessionCode}</span>
              </p>
              <Button
                variant="ghost"
                size="sm"
                onClick={copySessionCode}
                className="text-primary hover:bg-primary/10 p-1"
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex items-center gap-4 mt-2">
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  connectionStatus === "connected" ? "bg-green-500" :
                  connectionStatus === "connecting" ? "bg-yellow-500 animate-pulse" :
                  "bg-red-500"
                }`}></div>
                <span className="text-sm text-muted-foreground">
                  {connectionStatus === "connected" ? "Connected" :
                   connectionStatus === "connecting" ? "Connecting..." :
                   "Disconnected"}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">{audienceCount} audience</span>
              </div>
              {sessionInfo && (
                <div className="text-sm text-muted-foreground">
                  {sessionInfo.input_language} → {sessionInfo.output_language}
                </div>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentStep("setup")}
              className="text-muted-foreground"
            >
              Settings
            </Button>
            <Button
              variant="outline"
              onClick={handleDisconnect}
              className="text-destructive border-destructive/30 hover:bg-destructive/10"
            >
              End Session
            </Button>
          </div>
        </div>

        <div className="grid gap-6">
          <Card className="shadow-medium border-border/50">
            <CardHeader>
              <CardTitle className="text-center">Voice Input</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <Button
                onClick={toggleRecording}
                size="lg"
                disabled={!isConnected}
                className={`w-24 h-24 rounded-full ${
                  isRecording
                    ? "bg-destructive hover:bg-destructive/90 text-destructive-foreground animate-pulse"
                    : "bg-gradient-primary hover:opacity-90 text-white disabled:opacity-50"
                }`}
              >
                {isRecording ? <MicOff className="w-8 h-8" /> : <Mic className="w-8 h-8" />}
              </Button>
              <p className="text-sm text-muted-foreground">
                {!isConnected
                  ? "Connecting to session..."
                  : isRecording
                    ? "Recording... Click to stop"
                    : "Click to start recording"}
              </p>
              {audioError && (
                <p className="text-sm text-destructive">{audioError}</p>
              )}
            </CardContent>
          </Card>

          {transcript && (
            <Card className="shadow-medium border-border/50">
              <CardHeader>
                <CardTitle>Current Speech</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-foreground bg-muted/50 p-4 rounded-lg border">
                  {transcript}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default Speaker;