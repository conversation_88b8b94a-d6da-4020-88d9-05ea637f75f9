version: '3.8'

services:
  # Redis for session data storage
  redis:
    image: redis:7-alpine
    container_name: live-translation-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - live-translation-network

  # Backend API service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: live-translation-backend
    restart: unless-stopped
    ports:
      - "8081:8000"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SPEECHMATICS_KEY=${SPEECHMATICS_KEY:-}
      - SM_WS_URL=${SM_WS_URL:-}
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - live-translation-network

  # Frontend React application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: live-translation-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - live-translation-network

# Named volumes for data persistence
volumes:
  redis_data:
    driver: local

# Custom network for service communication
networks:
  live-translation-network:
    driver: bridge