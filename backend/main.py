from fastapi import Fast<PERSON><PERSON>, WebSocket, WebSocketDisconnect, UploadFile
from fastapi.middleware.cors import CORSMiddleware
import redis.asyncio as redis
import uuid, json, httpx, os
import asyncio
import websockets

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
redis_client = redis.Redis(host=os.environ.get("REDIS_HOST", "localhost"), port=int(os.environ.get("REDIS_PORT", "6379")), decode_responses=True)
SPEECHMATICS_API_KEY = os.environ.get("SPEECHMATICS_KEY")
SM_WS_URL = os.environ.get("SM_WS_URL")
LANG = "cmn_en"
SAMPLE_RATE = 16000

def session_key(code): return f"session:{code}"

audience_websockets = {}  # {session_code: set()}


# Helper: Notify speaker of new audience count
async def broadcast_audience_count(code):
    key = f"{session_key(code)}:audience"
    count = await redis_client.scard(key)
    ws = audience_websockets.get(code, set())
    for w in ws:
        await w.send_json({"audience_count": count})


@app.post("/session/create")
async def create_session():
    code = str(uuid.uuid4())[:8]
    await redis_client.hset(session_key(code), mapping={"status": "active"})
    await redis_client.sadd(f"{session_key(code)}:audience", "__dummy__")
    audience_websockets[code] = set()
    return {"code": code}


@app.post("/session/join/{code}")
async def join_session(code: str, audience_id: str):
    await redis_client.sadd(f"{session_key(code)}:audience", audience_id)
    await broadcast_audience_count(code)
    return {"joined": True}


@app.websocket("/ws/audience/{code}")
async def ws_audience(websocket: WebSocket, code: str):
    await websocket.accept()
    audience_websockets.setdefault(code, set()).add(websocket)
    await broadcast_audience_count(code)
    pubsub = redis_client.pubsub()
    await pubsub.subscribe(f"{session_key(code)}:events")
    try:
        while True:
            msg = await pubsub.get_message(ignore_subscribe_messages=True, timeout=10)
            if msg:
                await websocket.send_text(msg["data"])
    except WebSocketDisconnect:
        audience_websockets[code].discard(websocket)
        await broadcast_audience_count(code)
        await pubsub.unsubscribe(f"{session_key(code)}:events")


@app.websocket("/ws/speaker/{code}")
async def ws_speaker(websocket: WebSocket, code: str):
    await websocket.accept()
    audio_buffer = bytearray()
    session_id = f"{code}_{id(websocket)}"

    all_segments = []
    await handle_live_mode(websocket, session_id, audio_buffer, all_segments)


@app.get("/session/state/{code}")
async def session_state(code: str):
    count = await redis_client.scard(f"{session_key(code)}:audience")
    data = await redis_client.hgetall(session_key(code))
    return {"audience_count": count, **data}

def transform_speechmatics_to_desired_format(sm_response):
    """
    Transform a Speechmatics response to the desired transcript segment format.

    Args:
        sm_response (dict): The response from Speechmatics.

    Returns:
        list: List of transformed transcript segments.
    """
    if sm_response.get("message") != "AddTranscript":
        return None
    results = sm_response.get("results", [])
    transformed_segments = []
    current_segment = None

    for result in results:
        if result.get("type") == "word":
            alternatives = result.get("alternatives", [])
            if alternatives:
                word_data = alternatives[0]
                speaker = word_data.get("speaker", "S1")
                content = word_data.get("content", "")
                start_time = result.get("start_time", 0)
                end_time = result.get("end_time", 0)

                # Aggregate words into a segment
                if current_segment and current_segment["speaker"] == speaker:
                    current_segment["text"] += " " + content
                    current_segment["end_time"] = end_time
                else:
                    if current_segment:
                        transformed_segments.append(current_segment)
                    current_segment = {
                        "text": content,
                        "speaker": speaker,
                        "start_time": start_time,
                        "end_time": end_time,
                    }

        elif result.get("type") == "punctuation":
            alternatives = result.get("alternatives", [])
            if alternatives:
                punctuation_data = alternatives[0]
                speaker = punctuation_data.get("speaker", "S1")
                content = punctuation_data.get("content", "")
                start_time = result.get("start_time", 0)
                end_time = result.get("end_time", 0)
                print(
                    f"🔤 [DEBUG] Punctuation: '{content}' Speaker: {speaker} Start Time: {start_time}, End Time: {end_time}"
                )
                if current_segment and current_segment["speaker"] == speaker:
                    current_segment["text"] += " " + content
                    current_segment["end_time"] = end_time
                else:
                    if current_segment:
                        transformed_segments.append(current_segment)
                    current_segment = {
                        "text": content,
                        "speaker": speaker,
                        "start_time": start_time,
                        "end_time": end_time,
                    }

    if current_segment:
        transformed_segments.append(current_segment)
    return transformed_segments


async def handle_live_mode(websocket, session_id, audio_buffer, all_segments):
    """Handle live transcription mode - existing Speechmatics integration."""
    seq_no = 0
    audio_url = None
    timeout_task = None

    try:
        print(f"[DEBUG] [{session_id}] Opening Speechmatics relay...")
        async with websockets.connect(
            SM_WS_URL,
            additional_headers={"Authorization": f"Bearer {SPEECHMATICS_API_KEY}"},
        ) as sm_ws:

            print(f"[DEBUG] [{session_id}] Connected to Speechmatics.")

            # 1. Send StartRecognition
            start_msg = {
                "message": "StartRecognition",
                "audio_format": {
                    "type": "raw",
                    "encoding": "pcm_s16le",
                    "sample_rate": SAMPLE_RATE,
                },
                "transcription_config": {
                    "language": LANG,
                    "operating_point": "enhanced",
                    "diarization": "speaker",
                    "enable_partials": True,
                    "max_delay": 2.0,
                },
            }
            await sm_ws.send(json.dumps(start_msg))

            # 2. Wait for RecognitionStarted
            while True:
                msg = await sm_ws.recv()
                try:
                    msg_obj = json.loads(msg)
                    if msg_obj.get("message") == "RecognitionStarted":
                        print(
                            f"[DEBUG] [{session_id}] Speechmatics RecognitionStarted."
                        )
                        break
                    else:
                        await safe_websocket_send(websocket, msg)
                except Exception as e:
                    print(f"[ERROR] [{session_id}] Error parsing initial message: {e}")

            async def wait_for_end_of_transcript():
                try:
                    await asyncio.sleep(30)
                    print(f"[DEBUG] [{session_id}] Timeout waiting for EndOfTranscript, processing anyway")
                except asyncio.CancelledError:
                    # Task was cancelled on normal completion; no problem.
                    print(f"[DEBUG] [{session_id}] Timeout task cancelled cleanly")
                    return


            # --- Main relay coroutines ---
            async def client_to_sm():
                """
                Relay audio data from client to Speechmatics, save/upload the audio buffer after stream ends.
                """
                nonlocal seq_no, audio_url
                try:
                    while True:
                        ws_msg = await websocket.receive()
                        if ws_msg["type"] == "websocket.disconnect":
                            print(
                                f"[DEBUG] [{session_id}] WebSocket disconnect received"
                            )
                            await websocket.close()
                            break
                        elif "bytes" in ws_msg and ws_msg["bytes"]:
                            audio_buffer.extend(ws_msg["bytes"])
                            await sm_ws.send(ws_msg["bytes"])
                            seq_no += 1
                        elif "text" in ws_msg and ws_msg["text"]:
                            if ws_msg["text"] == "END":
                                await sm_ws.send(
                                    json.dumps(
                                        {
                                            "message": "EndOfStream",
                                            "last_seq_no": seq_no,
                                        }
                                    )
                                )
                                break
                except Exception as e:
                    print(f"[ERROR] [{session_id}] Client audio relay error: {e}")

            async def sm_to_client():
                """
                Relay messages from Speechmatics to client, generate summary, and send final messages.

                Returns:
                    None
                """
                nonlocal all_segments, timeout_task, audio_url
                try:
                    async for msg in sm_ws:
                        try:
                            msg_obj = json.loads(msg)
                            message_type = msg_obj.get("message")
                            if message_type == "AddTranscript":
                                segments = transform_speechmatics_to_desired_format(
                                    msg_obj
                                )
                                if segments:
                                    all_segments.extend(segments)
                                partial_response = {
                                    "message": "PartialTranscript",
                                    "segments": segments or [],
                                    "all_segments": all_segments,
                                }
                                await safe_websocket_send(
                                    websocket, json.dumps(partial_response)
                                )
                            elif message_type == "AddPartialTranscript":
                                segments = transform_speechmatics_to_desired_format(
                                    msg_obj
                                )
                                partial_response = {
                                    "message": "AddPartialTranscript",
                                    "segments": segments or [],
                                }
                                await safe_websocket_send(
                                    websocket, json.dumps(partial_response)
                                )
                            else:
                                await safe_websocket_send(websocket, msg)
                        except json.JSONDecodeError:
                            await safe_websocket_send(websocket, msg)
                        except Exception as e:
                            print(
                                f"[ERROR] [{session_id}] Error processing Speechmatics message: {e}"
                            )
                except Exception as e:
                    print(f"[ERROR] [{session_id}] Speechmatics relay error: {e}")
                finally:
                    try:
                        await sm_ws.close()
                    except Exception as e:
                        print(f"[ERROR] Error closing Speechmatics relay: {e}")

            timeout_task = asyncio.create_task(wait_for_end_of_transcript())
            await asyncio.gather(client_to_sm(), sm_to_client(), timeout_task)

    except Exception as e:
        print(f"[ERROR] [{session_id}] Live mode error: {e}")
        await safe_websocket_send(websocket, json.dumps({
            "message": "Error",
            "error": str(e)
        }))

async def safe_websocket_send(websocket, message):
    """
    Safely send a message via WebSocket and handle errors.

    Args:
        websocket (WebSocket): FastAPI WebSocket object.
        message (str): The message to send.

    Returns:
        bool: True if send succeeds, False otherwise.
    """
    try:
        await websocket.send_text(message)
        return True
    except Exception as e:
        print(f"🔍 DEBUG: Error sending WebSocket message: {e}")
        return False



@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8081, reload=False)
